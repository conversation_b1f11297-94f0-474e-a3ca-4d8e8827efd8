import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { ApiResponse } from '@/types'

interface RouteParams {
  params: {
    id: string
  }
}

interface HabitHistoryData {
  completions: Array<{
    id: string
    completedAt: string
    notes?: string
  }>
  streaks: Array<{
    id: string
    startDate: string
    endDate?: string
    length: number
    isActive: boolean
  }>
  stats: {
    totalCompletions: number
    currentStreak: number
    longestStreak: number
    completionRate: number
    lastCompleted?: string
  }
}

// GET /api/habits/[id]/history - Get habit completion history and stats
export async function GET(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const limit = parseInt(searchParams.get('limit') || '30')
    const offset = parseInt(searchParams.get('offset') || '0')

    // Check if habit exists and belongs to user
    const habit = await prisma.habit.findFirst({
      where: {
        id: params.id,
        userId: session.user.id
      }
    })

    if (!habit) {
      return NextResponse.json(
        { success: false, error: 'Habit not found' },
        { status: 404 }
      )
    }

    // Get completions with pagination
    const completions = await prisma.habitCompletion.findMany({
      where: {
        habitId: params.id,
        userId: session.user.id
      },
      orderBy: { completedAt: 'desc' },
      take: limit,
      skip: offset,
      select: {
        id: true,
        completedAt: true,
        notes: true
      }
    })

    // Get all streaks for this habit
    const streaks = await prisma.streak.findMany({
      where: {
        habitId: params.id,
        userId: session.user.id
      },
      orderBy: { startDate: 'desc' },
      select: {
        id: true,
        startDate: true,
        endDate: true,
        length: true,
        isActive: true
      }
    })

    // Calculate stats
    const totalCompletions = await prisma.habitCompletion.count({
      where: {
        habitId: params.id,
        userId: session.user.id
      }
    })

    const currentStreak = streaks.find(s => s.isActive)?.length || 0
    const longestStreak = Math.max(0, ...streaks.map(s => s.length))

    // Calculate completion rate based on habit frequency and creation date
    const habitCreatedAt = habit.createdAt
    const now = new Date()
    const daysSinceCreation = Math.floor((now.getTime() - habitCreatedAt.getTime()) / (1000 * 60 * 60 * 24))
    
    let expectedCompletions = 0
    switch (habit.frequency) {
      case 'DAILY':
        expectedCompletions = daysSinceCreation
        break
      case 'WEEKLY':
        expectedCompletions = Math.floor(daysSinceCreation / 7)
        break
      case 'MONTHLY':
        expectedCompletions = Math.floor(daysSinceCreation / 30)
        break
    }

    const completionRate = expectedCompletions > 0 ? (totalCompletions / expectedCompletions) * 100 : 0

    const lastCompletion = completions[0]?.completedAt

    const historyData: HabitHistoryData = {
      completions: completions.map(c => ({
        id: c.id,
        completedAt: c.completedAt.toISOString(),
        notes: c.notes || undefined
      })),
      streaks: streaks.map(s => ({
        id: s.id,
        startDate: s.startDate.toISOString(),
        endDate: s.endDate?.toISOString(),
        length: s.length,
        isActive: s.isActive
      })),
      stats: {
        totalCompletions,
        currentStreak,
        longestStreak,
        completionRate: Math.min(100, Math.round(completionRate)),
        lastCompleted: lastCompletion
      }
    }

    const response: ApiResponse<HabitHistoryData> = {
      success: true,
      data: historyData
    }

    return NextResponse.json(response)

  } catch (error) {
    console.error('Error fetching habit history:', error)
    
    const response: ApiResponse = {
      success: false,
      error: 'Failed to fetch habit history'
    }

    return NextResponse.json(response, { status: 500 })
  }
}
