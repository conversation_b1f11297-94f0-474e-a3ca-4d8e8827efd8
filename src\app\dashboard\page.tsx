'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { useEffect, useState } from 'react'
import { HabitList } from '@/components/habits/HabitList'
import { Button } from '@/components/ui/Button'
import { HabitWithCategory, ApiResponse } from '@/types'

export default function Dashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()
  const [habits, setHabits] = useState<HabitWithCategory[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (status === 'unauthenticated') {
      router.push('/')
    }
  }, [status, router])

  useEffect(() => {
    if (session?.user?.id) {
      fetchHabits()
    }
  }, [session])

  const fetchHabits = async () => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/habits')
      const result: ApiResponse<HabitWithCategory[]> = await response.json()

      if (result.success) {
        setHabits(result.data || [])
      } else {
        setError(result.error || 'Failed to fetch habits')
      }
    } catch (err) {
      setError('Failed to fetch habits')
    } finally {
      setIsLoading(false)
    }
  }

  const handleToggleComplete = async (habitId: string) => {
    try {
      const response = await fetch(`/api/habits/${habitId}/complete`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({})
      })

      const result: ApiResponse = await response.json()

      if (result.success) {
        // Refresh habits to show updated completion status
        await fetchHabits()
      } else {
        setError(result.error || 'Failed to complete habit')
      }
    } catch (err) {
      setError('Failed to complete habit')
    }
  }

  const handleUncomplete = async (habitId: string) => {
    try {
      const response = await fetch(`/api/habits/${habitId}/complete`, {
        method: 'DELETE'
      })

      const result: ApiResponse = await response.json()

      if (result.success) {
        // Refresh habits to show updated completion status
        await fetchHabits()
      } else {
        setError(result.error || 'Failed to uncomplete habit')
      }
    } catch (err) {
      setError('Failed to uncomplete habit')
    }
  }

  const handleHabitUpdated = (updatedHabit: HabitWithCategory) => {
    setHabits(prev => prev.map(h => h.id === updatedHabit.id ? updatedHabit : h))
  }

  const handleDeleteHabit = async (habitId: string) => {
    if (!confirm('Are you sure you want to delete this habit?')) {
      return
    }

    try {
      const response = await fetch(`/api/habits/${habitId}`, {
        method: 'DELETE'
      })

      const result: ApiResponse = await response.json()

      if (result.success) {
        setHabits(prev => prev.filter(h => h.id !== habitId))
      } else {
        setError(result.error || 'Failed to delete habit')
      }
    } catch (err) {
      setError('Failed to delete habit')
    }
  }

  if (status === 'loading') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    )
  }

  if (!session) {
    return null
  }

  // Calculate stats
  const totalHabits = habits.length
  const activeHabits = habits.filter(h => h.isActive).length
  const todayCompletions = habits.filter(h => {
    const today = new Date()
    today.setHours(0, 0, 0, 0)
    return h.completions?.some(c => {
      const completionDate = new Date(c.completedAt)
      completionDate.setHours(0, 0, 0, 0)
      return completionDate.getTime() === today.getTime()
    })
  }).length

  const currentStreaks = habits.map(h => {
    const activeStreak = h.streaks?.find(s => s.isActive)
    return activeStreak?.length || 0
  })
  const maxStreak = Math.max(0, ...currentStreaks)

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      {/* Welcome Section */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-2">
          Welcome back, {session?.user?.name?.split(' ')[0]}! 👋
        </h1>
        <p className="text-gray-600">
          Ready to build some momentum today?
        </p>
      </div>

      {/* Error Message */}
      {error && (
        <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
          <p className="text-red-800">{error}</p>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setError(null)}
            className="mt-2"
          >
            Dismiss
          </Button>
        </div>
      )}

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-blue-100 rounded-lg">
              <div className="text-blue-600 text-xl">📊</div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Habits</p>
              <p className="text-2xl font-bold text-gray-900">{totalHabits}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-green-100 rounded-lg">
              <div className="text-green-600 text-xl">✅</div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Completed Today</p>
              <p className="text-2xl font-bold text-gray-900">{todayCompletions}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-orange-100 rounded-lg">
              <div className="text-orange-600 text-xl">🔥</div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Best Streak</p>
              <p className="text-2xl font-bold text-gray-900">{maxStreak}</p>
            </div>
          </div>
        </div>

        <div className="bg-white p-6 rounded-lg shadow-md">
          <div className="flex items-center">
            <div className="p-2 bg-purple-100 rounded-lg">
              <div className="text-purple-600 text-xl">📈</div>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Habits</p>
              <p className="text-2xl font-bold text-gray-900">{activeHabits}</p>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="mt-8 bg-white p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold text-gray-900 mb-4">
          Quick Actions
        </h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <button
            onClick={() => router.push('/habits/generate')}
            className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors"
          >
            <div className="text-center">
              <div className="text-2xl mb-2">🤖</div>
              <h3 className="font-semibold text-gray-900">Generate AI Habits</h3>
              <p className="text-sm text-gray-600">Let AI create personalized habits for you</p>
            </div>
          </button>
          <button className="p-4 border-2 border-dashed border-gray-300 rounded-lg hover:border-blue-500 hover:bg-blue-50 transition-colors">
            <div className="text-center">
              <div className="text-2xl mb-2">➕</div>
              <h3 className="font-semibold text-gray-900">Add Custom Habit</h3>
              <p className="text-sm text-gray-600">Create your own habit manually</p>
            </div>
          </button>
        </div>
      </div>

      {/* Habits List */}
      {isLoading ? (
        <div className="mt-8 text-center py-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading your habits...</p>
        </div>
      ) : (
        <div className="mt-8">
          <HabitList
            habits={habits}
            onToggleComplete={handleToggleComplete}
            onUncomplete={handleUncomplete}
            onHabitUpdated={handleHabitUpdated}
            onDeleteHabit={handleDeleteHabit}
            title="Your Habits"
            emptyMessage="No habits yet! Generate some AI-powered habits or create your own to get started."
          />
        </div>
      )}
    </div>
  )
}
