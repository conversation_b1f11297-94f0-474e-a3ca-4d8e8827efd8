'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/Button'
import { ApiResponse } from '@/types'

interface HabitHistoryData {
  completions: Array<{
    id: string
    completedAt: string
    notes?: string
  }>
  streaks: Array<{
    id: string
    startDate: string
    endDate?: string
    length: number
    isActive: boolean
  }>
  stats: {
    totalCompletions: number
    currentStreak: number
    longestStreak: number
    completionRate: number
    lastCompleted?: string
  }
}

interface HabitHistoryProps {
  habitId: string
  habitTitle: string
  onClose: () => void
}

export function HabitHistory({ habitId, habitTitle, onClose }: HabitHistoryProps) {
  const [history, setHistory] = useState<HabitHistoryData | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchHistory()
  }, [habitId])

  const fetchHistory = async () => {
    try {
      setIsLoading(true)
      const response = await fetch(`/api/habits/${habitId}/history`)
      const result: ApiResponse<HabitHistoryData> = await response.json()

      if (result.success) {
        setHistory(result.data || null)
      } else {
        setError(result.error || 'Failed to fetch history')
      }
    } catch (err) {
      setError('Failed to fetch history')
    } finally {
      setIsLoading(false)
    }
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    })
  }

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    })
  }

  if (isLoading) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
          <div className="text-center py-8">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-4 text-gray-600">Loading history...</p>
          </div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4">
          <div className="text-center py-8">
            <div className="text-red-500 text-4xl mb-4">⚠️</div>
            <p className="text-red-600 mb-4">{error}</p>
            <Button onClick={onClose} variant="outline">
              Close
            </Button>
          </div>
        </div>
      </div>
    )
  }

  if (!history) {
    return null
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between mb-6">
          <h2 className="text-2xl font-bold text-gray-900">
            {habitTitle} - History & Stats
          </h2>
          <Button onClick={onClose} variant="outline" size="sm">
            ✕
          </Button>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-8">
          <div className="bg-blue-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-blue-600">
              {history.stats.totalCompletions}
            </div>
            <div className="text-sm text-blue-800">Total Completions</div>
          </div>
          
          <div className="bg-orange-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-orange-600">
              {history.stats.currentStreak}
            </div>
            <div className="text-sm text-orange-800">Current Streak</div>
          </div>
          
          <div className="bg-purple-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-purple-600">
              {history.stats.longestStreak}
            </div>
            <div className="text-sm text-purple-800">Longest Streak</div>
          </div>
          
          <div className="bg-green-50 p-4 rounded-lg text-center">
            <div className="text-2xl font-bold text-green-600">
              {history.stats.completionRate}%
            </div>
            <div className="text-sm text-green-800">Completion Rate</div>
          </div>
        </div>

        <div className="grid md:grid-cols-2 gap-6">
          {/* Recent Completions */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Recent Completions
            </h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {history.completions.length > 0 ? (
                history.completions.map((completion) => (
                  <div
                    key={completion.id}
                    className="p-3 bg-gray-50 rounded-lg border"
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900">
                        {formatDateTime(completion.completedAt)}
                      </span>
                      <span className="text-green-600">✓</span>
                    </div>
                    {completion.notes && (
                      <p className="text-sm text-gray-600 mt-1">
                        {completion.notes}
                      </p>
                    )}
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">
                  No completions yet
                </p>
              )}
            </div>
          </div>

          {/* Streak History */}
          <div>
            <h3 className="text-lg font-semibold text-gray-900 mb-4">
              Streak History
            </h3>
            <div className="space-y-2 max-h-64 overflow-y-auto">
              {history.streaks.length > 0 ? (
                history.streaks.map((streak) => (
                  <div
                    key={streak.id}
                    className={`p-3 rounded-lg border ${
                      streak.isActive 
                        ? 'bg-orange-50 border-orange-200' 
                        : 'bg-gray-50 border-gray-200'
                    }`}
                  >
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-gray-900">
                        {streak.length} day{streak.length !== 1 ? 's' : ''}
                      </span>
                      {streak.isActive && (
                        <span className="text-orange-600 text-sm font-medium">
                          🔥 Active
                        </span>
                      )}
                    </div>
                    <div className="text-sm text-gray-600">
                      {formatDate(streak.startDate)} - {' '}
                      {streak.endDate ? formatDate(streak.endDate) : 'Ongoing'}
                    </div>
                  </div>
                ))
              ) : (
                <p className="text-gray-500 text-center py-4">
                  No streaks yet
                </p>
              )}
            </div>
          </div>
        </div>

        {/* Last Completed */}
        {history.stats.lastCompleted && (
          <div className="mt-6 p-4 bg-blue-50 rounded-lg">
            <p className="text-blue-800">
              <span className="font-medium">Last completed:</span>{' '}
              {formatDateTime(history.stats.lastCompleted)}
            </p>
          </div>
        )}
      </div>
    </div>
  )
}
