'use client'

import { useMemo } from 'react'
import { HabitWithCategory } from '@/types'

interface HabitScheduleProps {
  habit: HabitWithCategory
  className?: string
}

export function HabitSchedule({ habit, className }: HabitScheduleProps) {
  const scheduleInfo = useMemo(() => {
    const now = new Date()
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
    
    // Check if completed today
    const completedToday = habit.completions?.some(completion => {
      const completionDate = new Date(completion.completedAt)
      const completionDay = new Date(completionDate.getFullYear(), completionDate.getMonth(), completionDate.getDate())
      return completionDay.getTime() === today.getTime()
    }) || false

    // Calculate next due date based on frequency
    let nextDue: Date | null = null
    let isDue = false
    let isOverdue = false

    switch (habit.frequency) {
      case 'DAILY':
        nextDue = new Date(today)
        if (!completedToday) {
          isDue = true
          // Check if overdue (past today)
          isOverdue = false // For daily habits, we don't consider them overdue on the same day
        } else {
          // Next due is tomorrow
          nextDue.setDate(nextDue.getDate() + 1)
        }
        break

      case 'WEEKLY':
        // Find the start of this week (Sunday)
        const startOfWeek = new Date(today)
        startOfWeek.setDate(today.getDate() - today.getDay())
        
        // Check if completed this week
        const completedThisWeek = habit.completions?.some(completion => {
          const completionDate = new Date(completion.completedAt)
          return completionDate >= startOfWeek && completionDate < new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000)
        }) || false

        if (!completedThisWeek) {
          nextDue = new Date(startOfWeek)
          nextDue.setDate(nextDue.getDate() + 6) // End of week (Saturday)
          isDue = true
          isOverdue = now > nextDue
        } else {
          // Next due is next week
          nextDue = new Date(startOfWeek)
          nextDue.setDate(nextDue.getDate() + 7) // Start of next week
        }
        break

      case 'MONTHLY':
        // Find the start of this month
        const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
        const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
        
        // Check if completed this month
        const completedThisMonth = habit.completions?.some(completion => {
          const completionDate = new Date(completion.completedAt)
          return completionDate >= startOfMonth && completionDate <= endOfMonth
        }) || false

        if (!completedThisMonth) {
          nextDue = new Date(endOfMonth)
          isDue = true
          isOverdue = now > endOfMonth
        } else {
          // Next due is next month
          nextDue = new Date(today.getFullYear(), today.getMonth() + 1, 1)
        }
        break
    }

    return {
      nextDue,
      isDue,
      isOverdue,
      completedToday,
      completedThisPeriod: habit.frequency === 'DAILY' ? completedToday : 
                          habit.frequency === 'WEEKLY' ? 
                            habit.completions?.some(completion => {
                              const completionDate = new Date(completion.completedAt)
                              const startOfWeek = new Date(today)
                              startOfWeek.setDate(today.getDate() - today.getDay())
                              return completionDate >= startOfWeek && completionDate < new Date(startOfWeek.getTime() + 7 * 24 * 60 * 60 * 1000)
                            }) || false :
                            habit.completions?.some(completion => {
                              const completionDate = new Date(completion.completedAt)
                              const startOfMonth = new Date(today.getFullYear(), today.getMonth(), 1)
                              const endOfMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
                              return completionDate >= startOfMonth && completionDate <= endOfMonth
                            }) || false
    }
  }, [habit])

  const formatDate = (date: Date) => {
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: date.getFullYear() !== new Date().getFullYear() ? 'numeric' : undefined
    })
  }

  const getStatusColor = () => {
    if (scheduleInfo.completedThisPeriod) return 'text-green-600 bg-green-50'
    if (scheduleInfo.isOverdue) return 'text-red-600 bg-red-50'
    if (scheduleInfo.isDue) return 'text-orange-600 bg-orange-50'
    return 'text-gray-600 bg-gray-50'
  }

  const getStatusText = () => {
    if (scheduleInfo.completedThisPeriod) {
      switch (habit.frequency) {
        case 'DAILY': return 'Completed today'
        case 'WEEKLY': return 'Completed this week'
        case 'MONTHLY': return 'Completed this month'
      }
    }
    
    if (scheduleInfo.isOverdue) {
      switch (habit.frequency) {
        case 'DAILY': return 'Due today'
        case 'WEEKLY': return 'Overdue this week'
        case 'MONTHLY': return 'Overdue this month'
      }
    }
    
    if (scheduleInfo.isDue) {
      switch (habit.frequency) {
        case 'DAILY': return 'Due today'
        case 'WEEKLY': return 'Due this week'
        case 'MONTHLY': return 'Due this month'
      }
    }
    
    if (scheduleInfo.nextDue) {
      return `Next due: ${formatDate(scheduleInfo.nextDue)}`
    }
    
    return 'No schedule'
  }

  return (
    <div className={`inline-flex items-center px-2 py-1 text-xs font-medium rounded-full ${getStatusColor()} ${className}`}>
      {getStatusText()}
    </div>
  )
}
